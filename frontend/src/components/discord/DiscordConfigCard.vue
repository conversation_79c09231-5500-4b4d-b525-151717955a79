<template>
  <v-card class="discord-config-card" elevation="2">
    <v-card-title class="d-flex align-center justify-space-between">
      <div class="d-flex align-center">
        <v-icon :color="config.enabled ? 'success' : 'grey'" class="me-2">
          {{ config.enabled ? 'mdi-discord' : 'mdi-discord-outline' }}
        </v-icon>
        <span>{{ config.source_name }}</span>
      </div>
      
      <div class="d-flex align-center">
        <v-chip
          :color="config.enabled ? 'success' : 'grey'"
          size="small"
          class="me-2"
        >
          {{ config.enabled ? '已启用' : '已禁用' }}
        </v-chip>
        
        <v-menu>
          <template v-slot:activator="{ props }">
            <v-btn
              icon="mdi-dots-vertical"
              size="small"
              variant="text"
              v-bind="props"
            />
          </template>
          
          <v-list>
            <v-list-item @click="$emit('edit', config)">
              <v-list-item-title>
                <v-icon class="me-2">mdi-pencil</v-icon>
                编辑
              </v-list-item-title>
            </v-list-item>
            
            <v-list-item @click="$emit('toggle', config.id)">
              <v-list-item-title>
                <v-icon class="me-2">
                  {{ config.enabled ? 'mdi-pause' : 'mdi-play' }}
                </v-icon>
                {{ config.enabled ? '禁用' : '启用' }}
              </v-list-item-title>
            </v-list-item>
            
            <v-divider />
            
            <v-list-item @click="$emit('delete', config.id)" class="text-error">
              <v-list-item-title>
                <v-icon class="me-2">mdi-delete</v-icon>
                删除
              </v-list-item-title>
            </v-list-item>
          </v-list>
        </v-menu>
      </div>
    </v-card-title>

    <v-card-text>
      <div class="config-details">
        <!-- Token状态 -->
        <div class="detail-row">
          <v-icon class="me-2" size="small">mdi-key</v-icon>
          <span class="detail-label">Token:</span>
          <v-chip
            :color="config.has_token ? 'success' : 'warning'"
            size="small"
            class="ms-2"
          >
            {{ config.has_token ? '已配置' : '未配置' }}
          </v-chip>
        </div>

        <!-- 服务器过滤 -->
        <div class="detail-row" v-if="config.server_ids.length > 0">
          <v-icon class="me-2" size="small">mdi-server</v-icon>
          <span class="detail-label">服务器:</span>
          <span class="detail-value">{{ config.server_ids.length }} 个</span>
        </div>

        <!-- 频道过滤 -->
        <div class="detail-row" v-if="config.channel_ids.length > 0">
          <v-icon class="me-2" size="small">mdi-pound</v-icon>
          <span class="detail-label">频道:</span>
          <span class="detail-value">{{ config.channel_ids.length }} 个</span>
        </div>

        <!-- 作者过滤 -->
        <div class="detail-row" v-if="config.author_ids.length > 0">
          <v-icon class="me-2" size="small">mdi-account</v-icon>
          <span class="detail-label">作者:</span>
          <span class="detail-value">{{ config.author_ids.length }} 个</span>
        </div>

        <!-- 消息类型 -->
        <div class="detail-row">
          <v-icon class="me-2" size="small">mdi-message</v-icon>
          <span class="detail-label">消息类型:</span>
          <div class="ms-2">
            <v-chip
              v-for="type in config.allowed_message_types"
              :key="type"
              size="x-small"
              class="me-1"
            >
              {{ getMessageTypeLabel(type) }}
            </v-chip>
          </div>
        </div>

        <!-- 创建时间 -->
        <div class="detail-row">
          <v-icon class="me-2" size="small">mdi-clock</v-icon>
          <span class="detail-label">创建时间:</span>
          <span class="detail-value">{{ formatDate(config.created_at) }}</span>
        </div>
      </div>
    </v-card-text>
  </v-card>
</template>

<script setup lang="ts">
import type { DiscordConfig } from '@/stores/discordConfig'

interface Props {
  config: DiscordConfig
}

defineProps<Props>()

defineEmits<{
  edit: [config: DiscordConfig]
  toggle: [id: string]
  delete: [id: string]
}>()

const getMessageTypeLabel = (type: string): string => {
  const labels: Record<string, string> = {
    text: '文本',
    embed: '嵌入',
    attachment: '附件',
    reply: '回复'
  }
  return labels[type] || type
}

const formatDate = (dateString: string): string => {
  return new Date(dateString).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}
</script>

<style scoped>
.discord-config-card {
  margin-bottom: 16px;
}

.config-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.detail-row {
  display: flex;
  align-items: center;
  font-size: 0.875rem;
}

.detail-label {
  font-weight: 500;
  min-width: 80px;
}

.detail-value {
  color: rgba(var(--v-theme-on-surface), 0.7);
}
</style>
