<template>
  <div class="llm-config-panel" data-testid="llm-config-panel">
    <!-- 头部操作栏 -->
    <v-card class="mb-4" elevation="1">
      <v-card-text>
        <div class="d-flex align-center justify-space-between">
          <div class="d-flex align-center">
            <v-icon class="me-2" color="primary">mdi-brain</v-icon>
            <div>
              <h3 class="text-h6">LLM配置管理</h3>
              <p class="text-body-2 text-medium-emphasis mb-0">
                管理多个LLM服务提供商配置，支持DeepSeek、Gemini、ChatGPT、Claude
              </p>
            </div>
          </div>

          <v-btn
            color="primary"
            prepend-icon="mdi-plus"
            @click="openCreateDialog"
            data-testid="create-config-btn"
          >
            添加配置
          </v-btn>
        </div>
      </v-card-text>
    </v-card>



    <!-- 加载状态 -->
    <div v-if="llmStore.loading" class="text-center py-8">
      <v-progress-circular indeterminate color="primary" size="48" />
      <p class="text-body-2 text-medium-emphasis mt-4">加载配置中...</p>
    </div>

    <!-- 空状态 -->
    <div v-else-if="llmStore.configs.length === 0" class="text-center py-8">
      <v-icon size="64" color="grey-lighten-1">mdi-brain-outline</v-icon>
      <h3 class="text-h6 mt-4 mb-2">暂无LLM配置</h3>
      <p class="text-body-2 text-medium-emphasis mb-4">
        创建第一个LLM配置来开始使用AI功能
      </p>
      <v-btn color="primary" @click="openCreateDialog">
        创建配置
      </v-btn>
    </div>

    <!-- 配置列表 -->
    <v-row v-else>
      <v-col
        v-for="config in llmStore.configs"
        :key="config.id"
        cols="12"
        md="6"
        lg="4"
      >
        <LLMConfigCard
          :config="config"
          @edit="openEditDialog"
          @toggle="toggleConfig"
          @delete="deleteConfig"
        />
      </v-col>
    </v-row>

    <!-- 创建/编辑对话框 -->
    <LLMConfigDialog
      v-model="dialogVisible"
      :config="editingConfig"
      @save="saveConfig"
    />

    <!-- 删除确认对话框 -->
    <v-dialog v-model="deleteDialogVisible" max-width="500">
      <v-card>
        <v-card-title class="text-h5">
          <v-icon icon="mdi-delete" color="error" class="me-2" />
          确认删除
        </v-card-title>
        <v-card-text>
          <p>您确定要删除配置 <strong>{{ deletingConfig?.config_name }}</strong> 吗？</p>
          <p class="text-error mt-2">此操作不可撤销。</p>
        </v-card-text>
        <v-card-actions>
          <v-spacer />
          <v-btn variant="text" @click="deleteDialogVisible = false">
            取消
          </v-btn>
          <v-btn
            color="error"
            variant="flat"
            :loading="llmStore.loading"
            @click="confirmDelete"
          >
            删除
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import type { LLMConfig } from '@/types/llm.types'
import { useLLMConfigStore } from '@/stores/llmConfig'
import LLMConfigCard from './LLMConfigCard.vue'
import LLMConfigDialog from './LLMConfigDialog.vue'

// Store
const llmStore = useLLMConfigStore()

// 状态
const dialogVisible = ref(false)
const deleteDialogVisible = ref(false)
const editingConfig = ref<LLMConfig | undefined>()
const deletingConfig = ref<LLMConfig | undefined>()

// 方法
function openCreateDialog() {
  editingConfig.value = undefined
  dialogVisible.value = true
}

function openEditDialog(config: LLMConfig) {
  editingConfig.value = config
  dialogVisible.value = true
}

function deleteConfig(config: LLMConfig) {
  deletingConfig.value = config
  deleteDialogVisible.value = true
}

async function confirmDelete() {
  if (!deletingConfig.value) return

  try {
    await llmStore.deleteConfig(deletingConfig.value.id)
    deleteDialogVisible.value = false
    deletingConfig.value = undefined
  } catch (error) {
    // 错误已在store中处理
  }
}

async function saveConfig(config: LLMConfig) {
  try {
    if (editingConfig.value) {
      await llmStore.updateConfig(config.id, config)
    } else {
      await llmStore.createConfig(config)
    }
    dialogVisible.value = false
    editingConfig.value = undefined
  } catch (error) {
    // 错误已在store中处理
  }
}

async function toggleConfig(config: LLMConfig) {
  try {
    await llmStore.updateConfig(config.id, { ...config, enabled: !config.enabled })
  } catch (error) {
    // 错误已在store中处理
  }
}

// 生命周期
onMounted(async () => {
  try {
    await llmStore.fetchConfigs()
  } catch (error) {
    // 错误已在store中处理
  }
})
</script>

<style scoped>
/* 样式与Discord配置面板保持一致 */
</style>
